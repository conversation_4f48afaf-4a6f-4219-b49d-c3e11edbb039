import os
import json
import numpy as np
from typing import Dict, List, Union

from core.projection.core import ProjectionCore
from utils.labelme_processor import LabelmeProcessor
from detection.utils.bbox_converter import BboxConverter
from core.coordinate import CoordinateTransformer

class CubeToPanoConverter:
    def __init__(self, pano_width: int, pano_height: int, cube_size: int):
        self.pano_width = pano_width
        self.pano_height = pano_height
        self.cube_size = cube_size
        self.projection_engine = ProjectionCore(
            height=self.pano_height, width=self.pano_width, face_size=self.cube_size
        )
        self.coord_transformer = CoordinateTransformer()
        # LabelmeProcessor 只負責檔案 IO 和格式解析
        self.labelme_processor = LabelmeProcessor()
        # BboxConverter 專門負責邊界框的座標轉換
        self.bbox_converter = BboxConverter(coord_transformer=self.coord_transformer)

    def convert_cube_images_to_panorama(self, cube_faces: Dict[str, np.ndarray]) -> np.ndarray:
        """
        將六面立方體影像轉換為全景影像。
        Args:
            cube_faces (Dict[str, np.ndarray]): 包含六個立方體面影像的字典，
                                                鍵為面名稱 (F, R, B, L, U, D)。
        Returns:
            np.ndarray: 轉換後的全景影像。
        """
        # ProjectionEngine 期望輸入為水平排列的立方體影像，或是一個包含六個面的 NumPy 陣列
        # 這裡假設 cube_faces 字典的順序是 F, R, B, L, U, D
        # 如果不是，需要先進行排序或轉換
        ordered_cube_faces = [
            cube_faces["F"], cube_faces["R"], cube_faces["B"],
            cube_faces["L"], cube_faces["U"], cube_faces["D"]
        ]
        # 將列表轉換為 NumPy 陣列，ProjectionEngine 可以處理 (6, H, W, C) 格式
        cube_array = np.array(ordered_cube_faces)
        # 對於高解析度影像，強制使用更穩定的插值器以避免 OpenCV 內部錯誤
        force_stable = self.pano_width > 4096
        panorama_image = self.projection_engine.cubemap_to_equirect(
            cube_array,
            force_advanced_interpolator=force_stable
        )
        return panorama_image

    def convert_cube_labelme_to_panorama_labelme(
        self, cube_labelme_data: Dict[str, List[Dict]]
    ) -> List[Dict]:
        """
        將立方體面的 LabelMe 標註轉換為全景圖的 LabelMe 標註。
        Args:
            cube_labelme_data (Dict[str, List[Dict]]): 包含每個立方體面 LabelMe 標註的字典。
        Returns:
            List[Dict]: 轉換後的全景圖 LabelMe 標註列表。
        """
        panorama_annotations = []
        for face_name, annotations in cube_labelme_data.items():
            for ann in annotations:
                # 假設標註是矩形
                if 'points' in ann and len(ann['points']) > 0:
                    points = ann['points']
                    # 正確地從點列表中分離 x 和 y 座標
                    x_coords = [p for p in points]
                    y_coords = [p for p in points]
                    bbox = {
                        'label': ann.get('label', 'unknown'),
                        'x1': min(x_coords),
                        'y1': min(y_coords),
                        'x2': max(x_coords),
                        'y2': max(y_coords),
                        'group_id': ann.get('group_id'),
                        'flags': ann.get('flags', {})
                    }
                    converted_bbox = self.bbox_converter.convert_cube_face_bbox_to_panorama(
                        face_name, bbox, self.cube_size, self.pano_width, self.pano_height
                    )
                    if converted_bbox:
                        panorama_annotations.append(converted_bbox)

        return panorama_annotations

    def process_cube_to_panorama(
        self,
        cube_image_paths: Dict[str, str],
        cube_labelme_paths: Dict[str, str],
        output_pano_image_path: str,
        output_pano_labelme_path: str,
    ):
        """
        執行從立方體影像和 LabelMe 標註到全景圖的完整轉換流程。
        Args:
            cube_image_paths (Dict[str, str]): 包含六個立方體面影像路徑的字典。
            cube_labelme_paths (Dict[str, str]): 包含六個立方體面 LabelMe JSON 路徑的字典。
            output_pano_image_path (str): 輸出全景影像的路徑。
            output_pano_labelme_path (str): 輸出全景 LabelMe JSON 的路徑。
        """
        # 載入立方體影像
        cube_faces = {}
        for face_name, img_path in cube_image_paths.items():
            # 這裡需要一個影像載入函式，例如使用 OpenCV
            # 為了簡化，這裡假設 img_path 指向一個可以直接讀取的影像檔案
            # 實際應用中可能需要 cv2.imread 等
            try:
                import cv2
                cube_faces[face_name] = cv2.imread(img_path)
                if cube_faces[face_name] is None:
                    raise FileNotFoundError(f"無法載入影像: {img_path}")
            except ImportError:
                print("請安裝 OpenCV (pip install opencv-python) 以載入影像。")
                return
            except Exception as e:
                print(f"載入影像 {img_path} 時發生錯誤: {e}")
                return

        # 載入立方體 LabelMe 標註
        cube_labelme_data = {}
        for face_name, json_path in cube_labelme_paths.items():
            data = self.labelme_processor.load_labelme_file(json_path)
            if data:
                cube_labelme_data[face_name] = data.get('shapes', [])
            else:
                print(f"警告: 無法載入 {json_path}，將跳過該面的標註。")
                cube_labelme_data[face_name] = []

        # 轉換影像
        print("正在轉換立方體影像到全景影像...")
        panorama_image = self.convert_cube_images_to_panorama(cube_faces)
        print(f"全景影像轉換完成，形狀: {panorama_image.shape}")

        # 轉換標註
        print("正在轉換立方體 LabelMe 標註到全景 LabelMe 標註...")
        panorama_annotations = self.convert_cube_labelme_to_panorama_labelme(cube_labelme_data)
        print(f"全景 LabelMe 標註轉換完成，共 {len(panorama_annotations)} 個標註。")

        # 保存全景影像
        try:
            import cv2
            cv2.imwrite(output_pano_image_path, panorama_image)
            print(f"全景影像已保存至: {output_pano_image_path}")
        except ImportError:
            print("請安裝 OpenCV (pip install opencv-python) 以保存影像。")
        except Exception as e:
            print(f"保存全景影像 {output_pano_image_path} 時發生錯誤: {e}")

        # 保存全景 LabelMe 標註
        try:
            self.labelme_processor.save_labelme_file(
                shapes=panorama_annotations,
                output_path=output_pano_labelme_path,
                img_width=self.pano_width,
                img_height=self.pano_height,
                image_path=os.path.basename(output_pano_image_path)
            )
            print(f"全景 LabelMe 標註已保存至: {output_pano_labelme_path}")
        except Exception as e:
            print(f"保存全景 LabelMe 標註 {output_pano_labelme_path} 時發生錯誤: {e}")

if __name__ == "__main__":
    # 範例使用
    # 假設您有以下立方體影像和 LabelMe JSON 檔案
    # 這些路徑需要替換為您實際的檔案路徑
    base_dir = r"D:\1_Panoramic_processing\1_final_code\re_code\recode_new\test_image\test"
    cube_image_paths = {
        "F": os.path.join(base_dir, "front.png"), # 假設這是前方的影像
        "R": os.path.join(base_dir, "right.png"), # 右方
        "B": os.path.join(base_dir, "back.png"), # 後方
        "L": os.path.join(base_dir, "left.png"), # 左方
        "U": os.path.join(base_dir, "up.png"), # 上方
        "D": os.path.join(base_dir, "down.png"), # 下方
    }

    # 假設您有對應的 LabelMe JSON 檔案
    cube_labelme_paths = {
        "F": os.path.join(base_dir, "front.json"),
        "R": os.path.join(base_dir, "right.json"),
        "B": os.path.join(base_dir, "back.json"),
        "L": os.path.join(base_dir, "left.json"),
        "U": os.path.join(base_dir, "up.json"),
        "D": os.path.join(base_dir, "down.json"),
    }

    output_dir = "test_output"
    os.makedirs(output_dir, exist_ok=True)
    output_pano_image_path = os.path.join(output_dir, "panorama_output.jpg")
    output_pano_labelme_path = os.path.join(output_dir, "panorama_output.json")

    # 定義輸出全景圖的尺寸和立方體面的尺寸
    PANO_WIDTH = 7680
    PANO_HEIGHT = 3840
    CUBE_SIZE = 1024 # 立方體面的邊長

    converter = CubeToPanoConverter(
        pano_width=PANO_WIDTH, pano_height=PANO_HEIGHT, cube_size=CUBE_SIZE
    )

    converter.process_cube_to_panorama(
        cube_image_paths,
        cube_labelme_paths,
        output_pano_image_path,
        output_pano_labelme_path,
    )