"""
智慧型快取系統模組 (core.interpolation.cache)

本模組提供一個專為插值運算設計的智慧型記憶體快取系統。
它採用 LRU (最近最少使用) 淘汰策略，並能根據數據的實際大小來自動管理記憶體，
確保快取不會無限制地增長，從而避免記憶體溢出。

主要特色：
- **執行緒安全**: 所有操作都由 `threading.Lock` 保護，可在多執行緒環境中安全使用。
- **LRU 淘汰策略**: 當快取達到容量上限時，會自動淘汰最久未被使用的項目。
- **動態記憶體管理**: 根據設定的記憶體上限（MB）和存入項目的大小來決定是否及如何淘汰舊項目。
- **內容感知金鑰**: 快取金鑰由影像尺寸、座標、插值方法和設定共同生成，確保快取的準確性。
"""

import hashlib
import threading
import time
from typing import Any, Dict, Optional, Tuple

import numpy as np
from log_utils.factory import get_logger

logger = get_logger(__name__)

class InterpolationCache:
    """
    一個執行緒安全的、基於 LRU 策略和記憶體大小限制的智慧型快取系統。
    """

    def __init__(self, max_size_mb: int = 512):
        """
        初始化快取系統。

        :param max_size_mb: 快取允許使用的最大記憶體量（以 MB 為單位）。
        """
        self.max_size_mb = max_size_mb
        self.cache: Dict[str, np.ndarray] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.Lock()
        logger.info(f"插值快取系統已初始化，最大容量: {max_size_mb} MB")

    def _get_key(
        self, image_shape: tuple, coords_hash: str, method: str, config_hash: str
    ) -> str:
        """根據輸入參數生成一個唯一的快取金鑰。"""
        return f"shape:{image_shape}_coords:{coords_hash}_method:{method}_config:{config_hash}"

    def _estimate_size_mb(self, data: np.ndarray) -> float:
        """估算給定 NumPy 陣列的大小（以 MB 為單位）。"""
        return data.nbytes / (1024 * 1024)

    def get(
        self,
        image_shape: tuple,
        coords: Tuple[np.ndarray, np.ndarray],
        method: str,
        config: dict,
    ) -> Optional[np.ndarray]:
        """
        嘗試從快取中獲取結果。如果命中，則更新其存取時間並返回結果的副本。

        :return: 快取中的 NumPy 陣列副本，如果未命中則返回 `None`。
        """
        with self.lock:
            # 使用 MD5 來高效地雜湊座標和設定，產生簡短的識別碼
            # 先將所有座標陣列串接成一個大的 NumPy 陣列，然後再轉換為位元組進行雜湊
            combined_coords = np.concatenate(coords, axis=None)
            coords_hash = hashlib.md5(combined_coords.tobytes()).hexdigest()[:16]
            config_hash = hashlib.md5(str(sorted(config.items())).encode()).hexdigest()[:8]
            key = self._get_key(image_shape, coords_hash, method, config_hash)

            if key in self.cache:
                logger.debug(f"快取命中: {key}")
                self.access_times[key] = time.time()
                # 返回副本以防止外部修改快取中的原始數據
                return self.cache[key].copy()
            
            logger.debug(f"快取未命中: {key}")
            return None

    def put(
        self,
        image_shape: tuple,
        coords: Tuple[np.ndarray, np.ndarray],
        method: str,
        config: dict,
        result: np.ndarray,
    ):
        """
        將一個計算結果存入快取。

        在存入前，會檢查空間是否足夠。如果不足，會根據 LRU 策略淘汰舊的項目。
        """
        with self.lock:
            combined_coords = np.concatenate(coords, axis=None)
            coords_hash = hashlib.md5(combined_coords.tobytes()).hexdigest()[:16]
            config_hash = hashlib.md5(str(sorted(config.items())).encode()).hexdigest()[:8]
            key = self._get_key(image_shape, coords_hash, method, config_hash)

            result_size = self._estimate_size_mb(result)

            # 如果單一項目就超過快取大小，則直接放棄快取，避免無限循環的淘汰
            if result_size > self.max_size_mb:
                logger.warning(f"項目大小 ({result_size:.2f}MB) 超過快取總容量 ({self.max_size_mb}MB)，將不進行快取。")
                return

            # 根據 LRU 策略清理快取，直到有足夠的空間容納新項目
            while (self._get_total_size_mb() + result_size > self.max_size_mb) and self.cache:
                # 找到存取時間最早（最久未使用）的項目
                oldest_key = min(self.access_times, key=self.access_times.get)
                logger.debug(f"快取已滿，淘汰最久未使用的項目: {oldest_key}")
                del self.cache[oldest_key]
                del self.access_times[oldest_key]

            self.cache[key] = result.copy()
            self.access_times[key] = time.time()
            logger.debug(f"已快取項目: {key}, 大小: {result_size:.2f}MB")

    def _get_total_size_mb(self) -> float:
        """計算目前快取中所有項目的總大小。"""
        if not self.cache:
            return 0.0
        return sum(self._estimate_size_mb(data) for data in self.cache.values())

    def clear(self):
        """清空整個快取。"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            logger.info("插值快取已完全清空。")

# --- 全域單例實例 ---
# 提供一個全域的快取實例，方便在整個應用程式中共享。
_global_cache = InterpolationCache()

def clear_interpolation_cache():
    """清空全域插值快取。"""
    _global_cache.clear()

def get_cache_stats() -> Dict[str, Any]:
    """
    獲取目前全域快取的統計資訊。

    :return: 一個包含快取大小、項目數量和最大容量的字典。
    """
    return {
        "cache_size_mb": _global_cache._get_total_size_mb(),
        "cached_items": len(_global_cache.cache),
        "max_size_mb": _global_cache.max_size_mb,
    }
