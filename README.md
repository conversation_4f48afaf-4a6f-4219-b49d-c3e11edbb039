# Generate Pyramid - 全景圖處理工具

此工具可將全景圖轉換為立方體金字塔結構，支援人臉模糊處理和 Logo 覆蓋功能。

## 功能特色

- **全景圖到金字塔 (pano_to_cube)**：將全景圖轉換為立方體金字塔
- **立方體到金字塔 (cube_to_cube)**：處理已有的立方體影像
- **清單模式 (list_cube)**：根據 CSV/Excel 清單批次處理
- **人臉模糊**：自動檢測並模糊人臉
- **Logo 覆蓋**：在底面 (DOWN) 加上 Logo
- **雙重輸出**：支援主要和次要輸出路徑
- **進度顯示**：安靜模式和詳細模式

## 安裝說明

### 方法一：使用 EXE 檔案（推薦）
1. 執行 `build_exe.bat` 或 `python build_exe.py` 打包程式
2. 打包完成後，在 `dist` 資料夾中會產生 `generate_pyramid.exe`
3. 準備 `config.yaml` 配置檔案
4. 執行 `generate_pyramid.exe` 並輸入 config.yaml 檔案路徑

### 方法二：使用 Python 環境
1. 安裝 Python 3.8+
2. 安裝相依套件：`pip install -r requirements.txt`
3. 安裝專案套件（開發模式）：`pip install -e .`
4. 執行：`python generate_pyramid.py`

## 設定檔 (config.yaml)

```yaml
# 基本設定
mode: 'pano_to_cube'  # 選項: pano_to_cube, cube_to_cube, list_cube
display_mode: 'verbose'  # 選項: quiet, verbose

# 路徑設定
input_path: 'D:\\input'
primary_output_path: 'D:\\output'
secondary_output_path: ''  # 可選的次要輸出路徑

# 模型設定
models:
  model1_path: 'D:\\models\\best.pt'
  model2_path: 'D:\\models\\yolo12.pt'

detection:
  conf_threshold: 0.05

# Logo 設定
logo:
  path: 'D:\\logo\\logo.png'  # Logo 檔案路徑
  scale: 0.741  # 縮放比例

# 清單模式設定
list_cube_settings:
  list_file: 'D:\\data\\scenes.csv'
  list_mode: 'include'  # include 或 exclude
  list_columns: ['區', '場景']
```

## 使用說明

### 1. 全景圖到金字塔 (pano_to_cube)
- 輸入：全景圖檔案 (.jpg, .png, .jpeg, .bmp, .tif, .tiff)
- 輸出：立方體金字塔結構
- 資料夾結構：`input_path/影像檔案`

### 2. 立方體到金字塔 (cube_to_cube)
- 輸入：已有的立方體場景
- 輸出：更新後的金字塔結構
- 資料夾結構：`input_path/區/場景/html5/`

### 3. 清單模式 (list_cube)
- 輸入：CSV/Excel 清單檔案 + 立方體場景
- 輸出：根據清單選擇性處理
- 支援 include/exclude 模式

## 輸出結構

```
output_path/
├── scene_name/
│   ├── 0/          # FRONT 面金字塔
│   ├── 1/          # RIGHT 面金字塔
│   ├── 2/          # BACK 面金字塔
│   ├── 3/          # LEFT 面金字塔
│   ├── 4/          # UP 面金字塔
│   ├── 5/          # DOWN 面金字塔 (含 Logo)
│   ├── html5/      # 2048x2048 立方體面
│   ├── preview.jpg # 預覽圖
│   └── thumbnail.jpg # 縮圖
```

## 顯示模式

### 安靜模式 (quiet)
```
(1/10) image1.jpg
處理完成，處理時間: 12.34 秒
(2/10) image2.jpg
處理完成，處理時間: 11.56 秒
```

### 詳細模式 (verbose)
```
2024-07-08 10:30:15,123 - INFO - 正在處理 image1.jpg...
2024-07-08 10:30:25,456 - INFO - 正在載入 logo: 'D:\\logo\\logo.png'
2024-07-08 10:30:25,789 - INFO - Logo 載入成功，尺寸: (512, 512, 4)
2024-07-08 10:30:27,012 - INFO - image1.jpg 處理完成。
```

## 注意事項

1. **模型檔案**：需要有效的 YOLO 模型檔案進行人臉檢測
2. **記憶體需求**：處理大型影像時需要足夠的記憶體
3. **GPU 支援**：自動檢測並使用 CUDA（如果可用）
4. **檔案路徑**：Windows 路徑使用雙反斜線 `\\` 或單正斜線 `/`
5. **Logo 格式**：支援 PNG（含透明度）和 JPG 格式

## 故障排除

### 常見問題

1. **模型載入失敗**
   - 檢查模型檔案路徑是否正確
   - 確認模型檔案完整性

2. **Logo 未顯示**
   - 檢查 logo 檔案路徑和格式
   - 確認 logo 尺寸適當

3. **記憶體不足**
   - 降低影像解析度
   - 關閉其他程式釋放記憶體

4. **打包失敗**
   - 確認已安裝所有相依套件
   - 檢查 PyInstaller 版本相容性

### 日誌檔案
程式執行時會產生 `image_processing.log` 日誌檔案，記錄詳細的執行過程和錯誤訊息。

## 版本資訊
- 版本：1.0.0
- 更新日期：2024-07-08
- 相容性：Python 3.8+, Windows 10+

## 授權
此工具僅供內部使用。