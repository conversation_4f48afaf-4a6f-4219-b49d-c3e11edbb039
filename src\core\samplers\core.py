"""
增強型影像採樣器核心模組 - 整合新插值系統

本模組旨在提供專門化的影像採樣器，它們重寫了舊有的採樣邏輯，
在保留複雜邊界處理（如球面環繞、立方體面鄰接）能力的基礎上，
全面整合了新的高性能插值系統 (`AdvancedInterpolator`)。
這使得採樣過程不僅準確，而且能夠利用超過38種插值演算法及GPU加速。
"""

import warnings
from typing import Optional, Dict, Any, Union

# 延遲載入重型庫
def _import_heavy_libs():
    """延遲載入重型庫"""
    global cv2, np
    try:
        import cv2
        import numpy as np
        return True
    except ImportError:
        return False

# 延遲載入插值模組
def _import_interpolation():
    """延遲載入插值模組"""
    try:
        from ..interpolation import (AdvancedInterpolator, InterpolationConfig,
                                     InterpolationMethod)
        return AdvancedInterpolator, InterpolationConfig, InterpolationMethod
    except ImportError as e:
        import warnings
        warnings.warn(f"插值模組載入失敗: {e}", ImportWarning)
        return None, None, None

# 全域變數，將在需要時載入
cv2 = None
np = None
AdvancedInterpolator = None
InterpolationConfig = None
InterpolationMethod = None

# 嘗試載入 numpy 的類型提示
try:
    from numpy.typing import NDArray
except ImportError:
    NDArray = Any


class EnhancedEquirectSampler:
    """
    增強的全景圖專用採樣器。

    此類別專門用於從全景圖（Equirectangular）中根據給定的座標對映進行採樣。
    它最關鍵的特性是能夠正確處理全景圖在經度上的環繞邊界以及在緯度上的極點，
    確保在投影轉換時不會產生接縫或失真。
    """

    def _ensure_dependencies(self):
        """確保所有依賴都已載入"""
        global cv2, np, AdvancedInterpolator, InterpolationConfig, InterpolationMethod

        if cv2 is None or np is None:
            if not _import_heavy_libs():
                raise ImportError("無法載入必要的重型庫 (cv2, numpy)")

        if AdvancedInterpolator is None:
            AdvancedInterpolator, InterpolationConfig, InterpolationMethod = _import_interpolation()
            if AdvancedInterpolator is None:
                # 如果無法載入插值模組，使用簡化版本
                warnings.warn("插值模組不可用，將使用簡化功能", ImportWarning)

    def __init__(
        self,
        coor_x: NDArray,
        coor_y: NDArray,
        method = "bilinear",  # 使用字符串預設值避免 InterpolationMethod 依賴
        use_gpu: bool = False,
        cache_enabled: bool = True,
        precision: str = "float32",
        fallback_cv2: bool = True,
    ):
        """
        初始化全景圖採樣器。

        Args:
            coor_x: 目標點在來源全景圖中的 X 座標對映。
            coor_y: 目標點在來源全景圖中的 Y 座標對映。
            method: 要使用的插值方法。
            use_gpu: 是否嘗試使用 GPU 加速。
            cache_enabled: 是否為底層插值器啟用快取。
            precision: 計算精度 ('float16', 'float32', 'float64')。
            fallback_cv2: 對於 OpenCV 支援的幾種基本插值方法，是否優先使用
                          經過優化的 OpenCV `remap` 函數以提高性能。
        """
        # 確保依賴已載入
        self._ensure_dependencies()

        self.coor_x = coor_x.astype(np.float32)
        # 座標 +1 是為了與 `_pad_equirect` 函式中的邊界擴展相匹配。
        self.coor_y = (coor_y + 1).astype(np.float32)
        self.method = method
        self.use_gpu = use_gpu
        self.fallback_cv2 = fallback_cv2

        self._setup_backend()
        self._setup_interpolator(cache_enabled, precision)

    def _setup_backend(self):
        """
        設定後端。如果啟用了 OpenCV 回退且插值方法受支援，
        則預先計算 `cv2.remap` 所需的對映表以提升效率。
        """
        self._use_cv2_backend = False
        self._cv2_interpolation = None
        self._cv2_maps = None

        if self.fallback_cv2 and cv2 is not None:
            cv2_method_map = {
                "nearest": cv2.INTER_NEAREST, "linear": cv2.INTER_LINEAR,
                "bilinear": cv2.INTER_LINEAR, "cubic": cv2.INTER_CUBIC,
                "bicubic": cv2.INTER_CUBIC,
            }
            method_key = self.method.value if isinstance(self.method, InterpolationMethod) else self.method
            if method_key in cv2_method_map:
                try:
                    self._cv2_interpolation = cv2_method_map[method_key]
                    nn_interpolation = self._cv2_interpolation == cv2.INTER_NEAREST
                    # 預轉換對映表，避免在每次呼叫時重複計算
                    self._cv2_maps = cv2.convertMaps(self.coor_x, self.coor_y, cv2.CV_16SC2, nninterpolation=nn_interpolation)
                    self._use_cv2_backend = True
                except Exception as e:
                    warnings.warn(f"OpenCV 後端設置失敗，將使用 `AdvancedInterpolator`: {e}")

    def _setup_interpolator(self, cache_enabled: bool, precision: str):
        """
        根據設定初始化底層的 `AdvancedInterpolator`。
        如果使用 OpenCV 後端，則無需初始化。
        """
        if not self._use_cv2_backend:
            config = InterpolationConfig(
                method=self.method, use_gpu=self.use_gpu, cache_enabled=cache_enabled,
                precision=precision, memory_limit_mb=2048
            )
            self.interpolator = AdvancedInterpolator(config)
        else:
            self.interpolator = None

    def __call__(self, img: NDArray) -> NDArray:
        """
        執行採樣操作。

        Args:
            img: 來源全景圖 NumPy 陣列。

        Returns:
            根據初始化時的座標對映採樣生成的新圖像。
        """
        source_dtype = img.dtype
        if source_dtype == np.float16:
            img = img.astype(np.float32) # 許多插值操作不支援 float16

        # 對影像進行邊界擴展，以處理環繞邊界
        padded = self._pad_equirect(img)

        if self._use_cv2_backend and self._cv2_maps is not None:
            # 使用高效的 OpenCV remap 函數
            result = cv2.remap(padded, self._cv2_maps[0], self._cv2_maps[1], interpolation=self._cv2_interpolation)
        elif self.interpolator:
            # 使用更靈活但可能較慢的 AdvancedInterpolator
            if padded.ndim == 3:
                results = [self.interpolator.interpolate(padded[..., c], self.coor_x, self.coor_y) for c in range(padded.shape[2])]
                result = np.stack(results, axis=-1)
            else:
                result = self.interpolator.interpolate(padded, self.coor_x, self.coor_y)
        else:
             raise RuntimeError("無可用的插值後端。")

        return result.astype(source_dtype) if source_dtype == np.float16 else result

    def _pad_equirect(self, img: NDArray) -> NDArray:
        """
        對全景圖進行特殊的邊界擴展（Padding）。
        上下擴展一行，並將頂部和底部的像素進行經度上的半圈旋轉，
        以模擬球面的極點連接，從而實現無縫的極點採樣。
        """
        w = img.shape[1]
        padded = np.pad(img, ((1, 1), (0, 0)) + ((0, 0),) * (img.ndim - 2), mode="empty")
        padded[0, :] = np.roll(img[0, :], w // 2, axis=0)
        padded[-1, :] = np.roll(img[-1, :], w // 2, axis=0)
        return padded

    def get_method_info(self) -> Dict[str, Any]:
        """返回當前採樣器的設定資訊。"""
        info = {
            "method": str(self.method),
            "backend": "OpenCV" if self._use_cv2_backend else "AdvancedInterpolator",
            "use_gpu": self.use_gpu,
            "coordinate_shape": self.coor_x.shape,
        }
        if self.interpolator:
            info.update(self.interpolator.config.__dict__)
        return info


class EnhancedCubeFaceSampler:
    """
    增強的立方體面專用採樣器。

    此類別用於從一組立方體面中進行採樣。其核心挑戰是處理跨越不同面之間的
    邊界採樣，確保面與面之間的過渡是平滑且無縫的。
    """

    def _ensure_dependencies(self):
        """確保所有依賴都已載入"""
        global cv2, np, AdvancedInterpolator, InterpolationConfig, InterpolationMethod

        if cv2 is None or np is None:
            if not _import_heavy_libs():
                raise ImportError("無法載入必要的重型庫 (cv2, numpy)")

        if AdvancedInterpolator is None:
            AdvancedInterpolator, InterpolationConfig, InterpolationMethod = _import_interpolation()
            if AdvancedInterpolator is None:
                # 如果無法載入插值模組，使用簡化版本
                warnings.warn("插值模組不可用，將使用簡化功能", ImportWarning)

    def __init__(
        self,
        tp: NDArray,
        coor_x: NDArray,
        coor_y: NDArray,
        method = "bilinear",
        h: Optional[int] = None,
        w: Optional[int] = None,
        use_gpu: bool = False,
        cache_enabled: bool = True,
        precision: str = "float32",
        fallback_cv2: bool = True,
        force_advanced_interpolator: bool = False,
    ):
        """
        初始化立方體面採樣器。

        Args:
            tp: 面類型對映，指示每個目標點應從哪個來源面採樣。
            coor_x, coor_y: 目標點在對應來源面上的 XY 座標。
            method, use_gpu, etc.: 與 `EnhancedEquirectSampler` 相同的插值設定。
            h, w: 預期的單個立方體面的高和寬，用於驗證。
            force_advanced_interpolator: 是否強制使用 `AdvancedInterpolator`，即使在
                                         `fallback_cv2` 為 True 的情況下。這對於處理
                                         超高解析度影像時避免 OpenCV 的內部限制很有用。
        """
        # 確保依賴已載入
        self._ensure_dependencies()

        self.tp = tp
        self.coor_x = (coor_x + 1).astype(np.float32)
        self.coor_y = (coor_y + 1).astype(np.float32)
        self.method = method
        self.h, self.w = h, w
        self.use_gpu = use_gpu
        self.fallback_cv2 = fallback_cv2
        self.force_advanced_interpolator = force_advanced_interpolator

        self._setup_backend()
        self._setup_interpolator(cache_enabled, precision)

    def _setup_backend(self):
        """設定 OpenCV 後端（如果適用）。"""
        self._use_cv2_backend = False
        self._cv2_interpolation = None
        self._cv2_maps = None

        if self.fallback_cv2 and not self.force_advanced_interpolator and cv2 is not None:
            cv2_method_map = {
                "nearest": cv2.INTER_NEAREST, "linear": cv2.INTER_LINEAR,
                "bilinear": cv2.INTER_LINEAR, "cubic": cv2.INTER_CUBIC,
                "bicubic": cv2.INTER_CUBIC,
            }
            method_key = self.method.value if isinstance(self.method, InterpolationMethod) else self.method
            if method_key in cv2_method_map:
                try:
                    self._cv2_interpolation = cv2_method_map[method_key]
                    if self.h and self.w:
                        # 調整 Y 座標以對應垂直堆疊的填充圖像
                        adjusted_coor_y = self.coor_y + np.multiply(self.tp, self.h + 2, dtype=np.float32)
                    else:
                        adjusted_coor_y = self.coor_y
                    nn_interpolation = self._cv2_interpolation == cv2.INTER_NEAREST
                    self._cv2_maps = cv2.convertMaps(self.coor_x, adjusted_coor_y, cv2.CV_16SC2, nninterpolation=nn_interpolation)
                    self._use_cv2_backend = True
                except Exception as e:
                    warnings.warn(f"OpenCV 後端設置失敗，將使用高級插值器: {e}")

    def _setup_interpolator(self, cache_enabled: bool, precision: str):
        """設定 `AdvancedInterpolator` 後端。"""
        if not self._use_cv2_backend:
            config = InterpolationConfig(
                method=self.method, use_gpu=self.use_gpu, cache_enabled=cache_enabled,
                precision=precision, memory_limit_mb=4096
            )
            self.interpolator = AdvancedInterpolator(config)
        else:
            self.interpolator = None

    def __call__(self, cube_faces: NDArray) -> NDArray:
        """
        執行採樣操作。

        Args:
            cube_faces: 來源立方體面陣列，形狀為 (6, H, W, C) 或 (6, H, W)。

        Returns:
            採樣後的新圖像。
        """
        h, w = cube_faces.shape[1:3]
        if self.h and self.w and (h != self.h or w != self.w):
            raise ValueError(f"輸入的立方體面尺寸 {(h, w)} 與預期尺寸 {(self.h, self.w)} 不符。")

        source_dtype = cube_faces.dtype
        if source_dtype == np.float16:
            cube_faces = cube_faces.astype(np.float32)

        # 對立方體面進行邊界擴展，從相鄰面複製像素以實現無縫採樣
        padded = self._pad_cube_faces(cube_faces)

        if self._use_cv2_backend and self._cv2_maps is not None:
            # 將填充後的面垂直堆疊成一張大圖，以利用 OpenCV 的 `remap`
            v_img = padded.reshape(-1, padded.shape[-1]) if padded.ndim == 4 else padded.reshape(-1)
            result = cv2.remap(v_img, self._cv2_maps[0], self._cv2_maps[1], interpolation=self._cv2_interpolation)
        elif self.interpolator:
            # 對每個通道和每個面獨立進行插值
            if padded.ndim == 4:
                results = [self._interpolate_3d(padded[..., c], self.tp, self.coor_y, self.coor_x) for c in range(padded.shape[3])]
                result = np.stack(results, axis=-1)
            else:
                result = self._interpolate_3d(padded, self.tp, self.coor_y, self.coor_x)
        else:
            raise RuntimeError("無可用的插值後端。")

        return result.astype(source_dtype) if source_dtype == np.float16 else result

    def _interpolate_3d(self, data_3d: NDArray, tp: NDArray, coor_y: NDArray, coor_x: NDArray) -> NDArray:
        """
        對 3D 資料（一組 2D 面）進行插值。
        此函數會遍歷每個面，並僅對需要從該面採樣的點進行插值。
        """
        flat_tp, flat_y, flat_x = tp.flatten(), coor_y.flatten(), coor_x.flatten()
        result = np.zeros_like(flat_tp, dtype=data_3d.dtype)

        for face_idx in range(6):
            face_mask = flat_tp == face_idx
            if not np.any(face_mask):
                continue
            
            face_y, face_x = flat_y[face_mask], flat_x[face_mask]
            face_result = self.interpolator.interpolate(data_3d[face_idx], face_x, face_y)
            result[face_mask] = face_result.flatten()

        return result.reshape(tp.shape)

    def _pad_cube_faces(self, cube_faces: NDArray) -> NDArray:
        """
        對立方體面進行邊界擴展。這是一個複雜但關鍵的操作，
        它將每個面的邊緣像素用其相鄰面的對應邊緣像素來填充，
        從而使得在邊界處的插值能夠無縫過渡。
        """
        F, R, B, L, U, D = 0, 1, 2, 3, 4, 5
        pad_width = ((0, 0), (1, 1), (1, 1)) + ((0, 0),) * (cube_faces.ndim - 3)
        padded = np.pad(cube_faces, pad_width, mode='edge') # 先用 edge 模式填充，再用精確值覆蓋

        # 填充上/下邊界
        padded[F, 0, 1:-1], padded[F, -1, 1:-1] = padded[U, -2, 1:-1], padded[D, 1, 1:-1]
        padded[R, 0, 1:-1], padded[R, -1, 1:-1] = np.flip(padded[U, 1:-1, -2]), padded[D, 1:-1, -2]
        padded[B, 0, 1:-1], padded[B, -1, 1:-1] = np.flip(padded[U, 1, 1:-1]), np.flip(padded[D, -2, 1:-1])
        padded[L, 0, 1:-1], padded[L, -1, 1:-1] = padded[U, 1:-1, 1], np.flip(padded[D, 1:-1, 1])
        padded[U, 0, 1:-1], padded[U, -1, 1:-1] = np.flip(padded[B, 1, 1:-1]), padded[F, 1, 1:-1]
        padded[D, 0, 1:-1], padded[D, -1, 1:-1] = padded[F, -2, 1:-1], np.flip(padded[B, -2, 1:-1])

        # 填充左/右邊界
        padded[F, 1:-1, 0], padded[F, 1:-1, -1] = padded[L, 1:-1, -2], padded[R, 1:-1, 1]
        padded[R, 1:-1, 0], padded[R, 1:-1, -1] = padded[F, 1:-1, -2], padded[B, 1:-1, 1]
        padded[B, 1:-1, 0], padded[B, 1:-1, -1] = padded[R, 1:-1, -2], padded[L, 1:-1, 1]
        padded[L, 1:-1, 0], padded[L, 1:-1, -1] = padded[B, 1:-1, -2], padded[F, 1:-1, 1]
        padded[U, 1:-1, 0], padded[U, 1:-1, -1] = padded[L, 1, 1:-1], np.flip(padded[R, 1, 1:-1])
        padded[D, 1:-1, 0], padded[D, 1:-1, -1] = np.flip(padded[L, -2, 1:-1]), padded[R, -2, 1:-1]

        return padded

    def get_method_info(self) -> Dict[str, Any]:
        """返回當前採樣器的設定資訊。"""
        info = {
            "method": str(self.method),
            "backend": "OpenCV" if self._use_cv2_backend else "AdvancedInterpolator",
            "use_gpu": self.use_gpu,
            "coordinate_shape": self.coor_x.shape,
            "expected_size": (self.h, self.w) if self.h and self.w else "Any",
        }
        if self.interpolator:
            info.update(self.interpolator.config.__dict__)
        return info


def create_equirect_sampler(
    coor_x: NDArray, coor_y: NDArray, method = "bilinear", **kwargs: Any
) -> EnhancedEquirectSampler:
    """
    創建並返回一個 `EnhancedEquirectSampler` 實例的工廠函數。
    提供了對舊版 `method` (以整數 order 表示) 的相容性。
    """
    if isinstance(method, int):
        order_to_method = {0: "nearest", 1: "bilinear", 2: "quadratic", 3: "bicubic", 4: "quartic", 5: "quintic"}
        method = order_to_method.get(method, "bilinear")
    return EnhancedEquirectSampler(coor_x, coor_y, method=method, **kwargs)


def create_cube_face_sampler(
    tp: NDArray, coor_x: NDArray, coor_y: NDArray, method = "bilinear",
    h: Optional[int] = None, w: Optional[int] = None,
    force_advanced_interpolator: bool = False, **kwargs: Any
):
    """
    創建並返回一個 `EnhancedCubeFaceSampler` 實例的工廠函數。
    提供了對舊版 `method` (以整數 order 表示) 的相容性。
    """
    if isinstance(method, int):
        order_to_method = {0: "nearest", 1: "bilinear", 2: "quadratic", 3: "bicubic", 4: "quartic", 5: "quintic"}
        method = order_to_method.get(method, "bilinear")
    return EnhancedCubeFaceSampler(
        tp, coor_x, coor_y, method=method, h=h, w=w,
        force_advanced_interpolator=force_advanced_interpolator, **kwargs
    )
