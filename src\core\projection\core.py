"""
核心投影轉換模組 (Core Projection Module)

本模組是實現全景圖（Equirectangular）與立方體映射（Cubemap）之間相互轉換的核心。
`ProjectionCore` 類別作為一個高階協調器，整合了座標轉換、影像採樣及後端分派
（CPU/GPU）的功能，提供一個統一且高效的投影轉換介面。
"""

import sys
import time
import warnings
from functools import lru_cache
from pathlib import Path
from typing import Any, Literal, Optional, Union, Dict, List

import os
# 只在需要時設置 Numba 禁用
if os.environ.get("DISABLE_NUMBA") != "0":
    os.environ["DISABLE_NUMBA"] = "1"

import cv2
import numpy as np
from numpy.typing import NDArray
from ..coordinate import CoordinateTransformer
from ..samplers.core import create_cube_face_sampler, create_equirect_sampler
from .formats import format_cubemap, standardize_cube_input
from .gpu_backends import HAS_TORCH, ProjectionBackend

# 在測試環境中禁用可選組件以避免導入問題
import os
if os.environ.get("TESTING") == "1":
    HAS_GPU_MANAGER = False
    HAS_PERFORMANCE_MONITOR = False
else:
    # 嘗試導入可選的高級組件
    try:
        from utils.gpu_manager import DeviceInfo, DeviceType, get_gpu_manager
        HAS_GPU_MANAGER = True
    except ImportError:
        HAS_GPU_MANAGER = False

    try:
        from utils.unified_performance_monitor import UnifiedPerformanceMonitor
        HAS_PERFORMANCE_MONITOR = True
    except ImportError:
        HAS_PERFORMANCE_MONITOR = False

from log_utils.factory import get_logger

logger = get_logger(__name__)


class ProjectionCore:
    """
    投影轉換核心類 - CPU 與 GPU 的智慧協調器

    該類別封裝了從全景圖到立方體圖（e2c）以及從立方體圖到全景圖（c2e）的完整轉換流程。
    它能根據系統環境和設定，自動選擇最優的計算後端（PyTorch, CuPy, or CPU）。
    """

    def __init__(
        self,
        height: int,
        width: int,
        face_size: Optional[int] = None,
        mode: str = "bilinear",
        use_gpu: bool = True,
        preferred_device: Optional[str] = None,
    ):
        """
        初始化投影轉換器。

        Args:
            height: 來源全景圖的高度。
            width: 來源全景圖的寬度。
            face_size: 目標立方體圖每個面的邊長。若為 None，則自動設為 width // 4。
            mode: 使用的插值模式 (e.g., 'bilinear', 'bicubic')。
            use_gpu: 是否嘗試使用 GPU 加速。
            preferred_device: 偏好的設備類型 ('cuda', 'opencl', 'cpu')。
        """
        self.h = height
        self.w = width
        self.face_w = face_size if face_size is not None else width // 4
        self.mode = mode
        self.use_gpu = use_gpu
        self.device_info: Optional[DeviceInfo] = None

        self._setup_components(preferred_device)
        self.gpu_backend = ProjectionBackend(self)
        self.coord_transformer = CoordinateTransformer()
        
        # 惰性初始化採樣器
        self._e2c_sampler = None
        self._c2e_sampler = None

        self.performance_stats: Dict[str, Union[int, float]] = {
            "total_conversions": 0, "gpu_conversions": 0, "cpu_conversions": 0,
            "total_time": 0.0, "gpu_time": 0.0, "cpu_time": 0.0,
        }
        logger.info(f"投影轉換器初始化完成。GPU啟用狀態: {self.use_gpu}, 使用設備: {self.device_info.name if self.device_info else 'CPU'}")

    def _setup_components(self, preferred_device: Optional[str]):
        """根據系統環境和設定，初始化 GPU 管理器和性能監控器。"""
        if self.use_gpu and HAS_GPU_MANAGER:
            gpu_manager = get_gpu_manager()
            try:
                type_map = {"cuda": DeviceType.CUDA, "opencl": DeviceType.OPENCL, "cpu": DeviceType.CPU}
                preferred_type = type_map.get(preferred_device.lower()) if preferred_device else None

                # 估算所需記憶體
                memory_required = (self.h * self.w * 4 * 6) / (1024**2)

                self.device_info = gpu_manager.get_best_device(memory_required, preferred_type)

                if not self.device_info or self.device_info.device_type == DeviceType.CPU:
                    logger.warning("沒有找到合適的 GPU 設備，回退到 CPU")
                    self.use_gpu = False
            except Exception as e:
                logger.warning(f"GPU 設備自動選擇失敗: {e}。將回退到 CPU 模式。")
                self.use_gpu = False

        if HAS_PERFORMANCE_MONITOR:
            try:
                self.performance_monitor = UnifiedPerformanceMonitor()
                self.performance_monitor.start_monitoring()
            except Exception as e:
                logger.warning(f"性能監控器初始化失敗: {e}")

    def equirect_to_cubemap(
        self, e_img: NDArray, cube_format: str = "dict", force_cpu: bool = False
    ) -> Union[NDArray, List[NDArray], Dict[str, NDArray]]:
        """
        將全景圖轉換為立方體映射。

        Args:
            e_img: 輸入的全景圖 NumPy 陣列。
            cube_format: 輸出的立方體圖格式 ('dict', 'list', 'horizon', 'dice')。
            force_cpu: 是否強制使用 CPU 進行計算。

        Returns:
            根據 `cube_format` 參數返回不同格式的立方體圖。
        """
        start_time = time.time()
        squeeze = e_img.ndim == 2
        if squeeze:
            e_img = e_img[..., None]

        use_gpu = self.use_gpu and not force_cpu and self._is_gpu_beneficial(e_img.shape[:2])
        cubemap = None

        if use_gpu and self.device_info:
            try:
                backend_map = {
                    (DeviceType.CUDA, HAS_TORCH): self.gpu_backend.equirect_to_cubemap_pytorch,
                }
                backend_func = backend_map.get((self.device_info.device_type, True))
                if backend_func:
                    cubemap = backend_func(e_img, squeeze)
            except Exception as e:
                logger.warning(f"GPU 轉換失敗: {e}，回退到 CPU。")
                cubemap = None

        # 如果 GPU 未使用或轉換失敗，則回退到 CPU
        if cubemap is None:
            if use_gpu: # 如果嘗試過 GPU 但失敗了，則記錄一條訊息
                logger.info("GPU 轉換失敗或未產生有效結果，回退到 CPU。")
            cubemap = self._equirect_to_cubemap_cpu(e_img)
            self.performance_stats["cpu_conversions"] += 1
            self.performance_stats["cpu_time"] += time.time() - start_time
        else:
            # 僅在 GPU 成功時記錄
            self.performance_stats["gpu_conversions"] += 1
            self.performance_stats["gpu_time"] += time.time() - start_time

        self.performance_stats["total_conversions"] += 1
        self.performance_stats["total_time"] += time.time() - start_time

        return format_cubemap(cubemap, cube_format, self.face_w, squeeze)

    def _equirect_to_cubemap_cpu(self, e_img: NDArray) -> NDArray:
        """使用 CPU 執行全景圖到立方體圖的轉換。"""
        if self._e2c_sampler is None:
            self._e2c_sampler = create_equirect_sampler(*self._get_e2c_coords(), self.mode)
        
        # 對每個通道獨立進行採樣
        channels = [self._e2c_sampler(e_img[..., i]) for i in range(e_img.shape[2])]
        return np.stack(channels, axis=-1, dtype=e_img.dtype)

    def cubemap_to_equirect(
        self,
        cube_faces: Union[Dict[str, NDArray], List[NDArray], NDArray],
        h: Optional[int] = None,
        w: Optional[int] = None,
        cube_format: str = "dict",
        force_advanced_interpolator: bool = False,
    ) -> NDArray:
        """
        將立方體映射轉換回全景圖。

        Args:
            cube_faces: 輸入的立方體圖，格式由 `cube_format` 指定。
            h: 輸出全景圖的高度。預設為初始化時的高度。
            w: 輸出全景圖的寬度。預設為初始化時的寬度。
            cube_format: 輸入的 `cube_faces` 的格式。
            force_advanced_interpolator: 是否強制使用備用插值器，以處理超高解析度。

        Returns:
            轉換後的 NumPy 全景圖陣列。
        """
        h, w = h or self.h, w or self.w
        cube_array = standardize_cube_input(cube_faces, self.face_w)

        if self._c2e_sampler is None:
            self._c2e_sampler = create_cube_face_sampler(
                *self._get_c2e_coords(),
                self.mode,
                self.face_w,
                self.face_w,
                force_advanced_interpolator=force_advanced_interpolator
            )

        # 對每個通道獨立進行採樣
        channels = [self._c2e_sampler(cube_array[..., i]) for i in range(cube_array.shape[-1])]
        equirect = np.stack(channels, axis=-1)

        # 如果目標尺寸與預設不同，則進行縮放
        if (h, w) != (self.h, self.w):
            equirect = cv2.resize(equirect, (w, h), interpolation=cv2.INTER_LINEAR)

        return equirect

    def _is_gpu_beneficial(self, image_size: tuple[int, int]) -> bool:
        """根據圖像大小判斷使用 GPU 是否可能帶來性能優勢。"""
        # 降低閾值：當像素總數超過 25 萬時就使用 GPU
        return image_size[0] * image_size[1] > 250_000

    @lru_cache(maxsize=2)
    def _get_e2c_coords(self) -> tuple[NDArray, NDArray]:
        """計算並快取 e2c 轉換所需的座標對映。"""
        from core.coordinate.numba_kernels import xyz_to_uv, uv_to_coor
        xyz = self.coord_transformer.generate_cube_coordinates(self.face_w)
        u, v = xyz_to_uv(xyz)
        return uv_to_coor(u, v, self.h, self.w)

    @lru_cache(maxsize=2)
    def _get_c2e_coords(self) -> tuple[NDArray, NDArray, NDArray]:
        """計算並快取 c2e 轉換所需的座標對映。"""
        u, v = self.coord_transformer.generate_equirect_grid(self.h, self.w)
        face_type = self.coord_transformer.compute_face_type(self.h, self.w)
        coor_x, coor_y = self.coord_transformer.compute_cube_coordinates(u, v, face_type, self.face_w)
        return face_type, coor_x, coor_y


@lru_cache(maxsize=16)
def create_projection_core(
    height: int, width: int, face_size: Optional[int] = None, mode: str = "bilinear"
) -> ProjectionCore:
    """
    工廠函數，用於創建和快取 `ProjectionCore` 實例。
    使用 `lru_cache` 可以避免對相同參數重複創建實例，提升效率。
    """
    return ProjectionCore(height, width, face_size, mode)
