"""
Utils Module - 企業級統一工具模組

此 `__init__.py` 檔案是 `utils` 模組的總入口，提供統一套現代化統一且高性能的
基礎設施元件，用於支援整個專案的記憶體管理、性能監控和分散式處理。

## 設計理念

- **統一介面 (Unified Interface)**: 將相似功能的管理器（如 `AdvancedMemoryManager`, `SmartPerformanceMonitor`）統一為新一代管理器（`UnifiedMemoryManager`, `UnifiedPerformanceMonitor`）提供一致的 API。
- **依賴注入 (Dependency Injection)**: 透過 `ManagerContainer` (一個DI 容器) 來管理各管理器的生命週期，實現高度松耦合。
- **向後相容 (Backward Compatibility)**: 對於已棄用的舊管理器提供了相容層，確保舊式碼無需修改即可運行，同時透過 `DeprecationWarning` 提示開發者升級。
- **便捷存取**: 透過 `get_memory_manager()` 等便利函式，讓使用者可以輕鬆取得管理器實例。

## 主要元件

- **UnifiedMemoryManager**: 統一的記憶體管理器，整合記憶體池和快取以及 GPU/CPU 記憶體調度
- **UnifiedPerformanceMonitor**: 統一的性能監控器，支援不同監控級別，並能追蹤CPU、GPU使用率和I/O
- **UnifiedDistributedProcessor**: 統一的分散式處理器，支援多種後端（`threading`, `multiprocessing`）
- **ManagerContainer**: 依賴注入容器，負責建立和管理各管理器的實例
- **UtilsConfig**: 用於設定 `utils` 模組各組件的強類型設定對象

## 建議使用模式

```python
from utils import get_performance_monitor, get_memory_manager, MemoryStrategy

# 1. 取得性能監控器並監控一個計算塊
monitor = get_performance_monitor()
with monitor.scope("heavy_computation"):
    # ... 執行重計算 ...
    pass

# 2. 取得記憶體管理器並配置塊記憶體
memory_manager = get_memory_manager()
gpu_array = memory_manager.allocate(size=1024*1024*100, strategy=MemoryStrategy.PINNED)
# ... 使用 gpu_array ...
memory_manager.deallocate(gpu_array)

# 3. 一鍵設定環境
from utils import setup_utils_environment
managers = setup_utils_environment(config_path="config.yaml")
managers['performance'].log_summary()
```
"""

# --- 套件資訊 ---
__package__ = "utils"
__version__ = "2.0.0"
__author__ = "AI 部門 - 全景處理團隊"
__docformat__ = "restructuredtext"


# --- 核心元件導入 ---
import warnings

try:
    from .containers import ManagerContainer
    from .core.config import UtilsConfig
    from .unified_distributed_processor import ProcessingBackend, UnifiedDistributedProcessor
    from .unified_memory_manager import MemoryStrategy, UnifiedMemoryManager
    from .unified_performance_monitor import (MonitoringLevel,
                                              UnifiedPerformanceMonitor)

    # 建薦使用 DI 容器 - 預設實例
    container = ManagerContainer()

except ImportError as e:
    warnings.warn(f"無法導入 utils 核心元件: {e}。Utils 模組功能將受限。", ImportWarning)
    # 當匯入失敗，建立虛擬container 以避免AttributeError
    class DummyContainer:
        def __getattr__(self, name):
            raise RuntimeError(f"Utils 模組未正確初始化，無法存取'{name}'。")
    container = DummyContainer()


# --- 便捷存取函數 ---

def get_memory_manager() -> 'UnifiedMemoryManager':
    """取得統一記憶體管理器"""
    return container.memory_manager()

def get_performance_monitor() -> 'UnifiedPerformanceMonitor':
    """取得統一性能監控器"""
    return container.performance_monitor()

def get_distributed_processor() -> 'UnifiedDistributedProcessor':
    """取得統一分散式處理器"""
    return container.distributed_processor()

def setup_utils_environment(config_path: str | None = None) -> dict:
    """一鍵設置完整工具環境"""
    if config_path:
        container.core.config.from_yaml(config_path)
    
    return {
        'memory': get_memory_manager(),
        'performance': get_performance_monitor(),
        'distributed': get_distributed_processor()
    }

# ================ 已棄用模組(向後相容) ================
def _deprecated_import_warning(old_module: str, new_module: str):
    warnings.warn(
        f"{old_module} 已棄用，請使用 {new_module} 取代。"
        f"\n{old_module} is deprecated, please use {new_module} instead.",
        DeprecationWarning,
        stacklevel=3,
    )

class AdvancedMemoryManager:
    def __new__(cls, *args, **kwargs):
        _deprecated_import_warning(
            "utils.advanced_memory_manager.AdvancedMemoryManager",
            "utils.unified_memory_manager.UnifiedMemoryManager",
        )
        return get_memory_manager()

class SmartMemoryManager:
    def __new__(cls, *args, **kwargs):
        _deprecated_import_warning(
            "utils.smart_memory_manager.SmartMemoryManager",
            "utils.unified_memory_manager.UnifiedMemoryManager",
        )
        return get_memory_manager()

class PerformanceMonitor:
    def __new__(cls, *args, **kwargs):
        _deprecated_import_warning(
            "utils.performance_monitor.PerformanceMonitor",
            "utils.unified_performance_monitor.UnifiedPerformanceMonitor",
        )
        return get_performance_monitor()

class SmartPerformanceMonitor:
    def __new__(cls, *args, **kwargs):
        _deprecated_import_warning(
            "utils.smart_performance_monitor.SmartPerformanceMonitor",
            "utils.unified_performance_monitor.UnifiedPerformanceMonitor",
        )
        return get_performance_monitor()

class DistributedProcessor:
    def __new__(cls, *args, **kwargs):
        _deprecated_import_warning(
            "utils.distributed_processor.DistributedProcessor",
            "utils.unified_distributed_processor.UnifiedDistributedProcessor",
        )
        return get_distributed_processor()

class ModernDistributedProcessor:
    def __new__(cls, *args, **kwargs):
        _deprecated_import_warning(
            "utils.modern_distributed_processor.ModernDistributedProcessor",
            "utils.unified_distributed_processor.UnifiedDistributedProcessor",
        )
        return get_distributed_processor()


__all__ = [
    # 核心統一管理器
    "UnifiedMemoryManager",
    "UnifiedPerformanceMonitor", 
    "UnifiedDistributedProcessor",
    "UtilsConfig",
    # DI 容器
    "container",
    # 便捷函數
    "get_memory_manager",
    "get_performance_monitor",
    "get_distributed_processor",
    "setup_utils_environment",
    # 枚舉
    "MemoryStrategy",
    "MonitoringLevel",
    "ProcessingBackend",
    # 已棄用(向後相容)
    "AdvancedMemoryManager",
    "SmartMemoryManager",
    "PerformanceMonitor",
    "SmartPerformanceMonitor",
    "DistributedProcessor",
    "ModernDistributedProcessor",
]