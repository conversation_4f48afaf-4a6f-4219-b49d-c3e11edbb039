[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "panoramic-processing-suite"
version = "0.1.0"
description = "A full suite for processing panoramic images, including detection and blurring."
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "numpy>=1.21.0",
    "opencv-python>=4.5.0",
    "pillow>=8.0.0",
    "pyyaml>=6.0",
    "ultralytics>=8.0.0",
    "torch>=1.12.0",
    "torchvision>=0.13.0",
    "scipy>=1.8.0",
    "matplotlib>=3.5.0",
    "tqdm>=4.64.0",
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "mypy>=0.991",
    "pylint>=2.15.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/panoramic-processing-suite"
Repository = "https://github.com/yourusername/panoramic-processing-suite"
Documentation = "https://github.com/yourusername/panoramic-processing-suite#readme"
"Bug Tracker" = "https://github.com/yourusername/panoramic-processing-suite/issues"

[project.scripts]
panorama-process = "processing.panorama_processor:main"
panorama-batch = "processing.batch_processor:main"

[tool.setuptools]
# 自動發現所有包和子包

[tool.setuptools.packages.find]
# 配置 src 布局
where = ["src"]
exclude = ["test*", "*.egg-info*", "__pycache__*"]

[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["test"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "gpu: marks tests that require GPU (deselect with '-m \"not gpu\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["core", "config", "detection", "log_utils", "processing", "utils"]
omit = [
    "*/test*",
    "*/tests/*",
    "*/conftest.py",
    "*/__pycache__/*",
    "*/.*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]